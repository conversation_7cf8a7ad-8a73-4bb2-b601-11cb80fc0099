# .1024 Configuration file for project run commands, compilation and debug settings (optional);
# Any changes made will be auto-saved and take effect immediately.
# For more information, please refer to the documentation: https://docs.clacky.ai/clacky-workspace/configure

# Command to run when "Run" button clicked
run_commands: ['mvn install -Dmaven.test.skip=true']
# Command to install or update dependencies, will execute each time a new thread created to ensure dependencies up-to-date
dependency_command: cd ~/app 
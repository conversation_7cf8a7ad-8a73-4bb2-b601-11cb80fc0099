# 需求背景

Clacky PaaS CodeZone三层架构：@meta → Root Thread → Issue Thread基于写时复制的高效存储机制，在用户基于项目并行多issue thread开发时，能大幅节省存储空间，同提供了较快的fork thread速度。该套环境过于依赖中心化的NFS存储架构，导致在实际压测时到500级并发时，有40%+容器激活30s超时判断错误，容器启动时间为50s-120s。为了实现秒起10000级并发，需要对中心化的文件系统进行分流，需要分拆codezone到分布式镜像进行加速。

# 改造范围

1. 新增thread级镜像，分拆node modules、root fs定制依赖内容到镜像，减少NFS服务器压力
    1. 新项目rootThread容器创建：沿用现在基于公共镜像创建逻辑启动容器
    2. 项目issueThread容器创建：基于rootThread的容器镜像环境启动容器
    3. rootThread/issueThread镜像自动保存：容器生命周期到STOP_SUCCESS时，在docker server释放前，增加**用户无感，后台自动**使docker commit进行thread镜像打包，并docker push推送到容器镜像服务（生产环境使用**Amazon Elastic Container Registry，线开发环境可以使用自部署镜像服务**）
    4. rootThread/issueThread容器启动：基于已经保存好的thread镜像启动容器，要求进行支持容器
    5. 秒起容器：
        1. 支持热点项目、thread预拉镜像缓存，提升命中率
        2. 相同thread调度符合亲和性原则，尽量调度在到相同docker server提升thread缓存命中率
        3. 其它可能性措施
    6. 镜像存储管理：
        1. 不活跃thread删除节省存储费用，支持功能开关，如：删除、关闭thread、比如30天不使用
        2. 用户按级别的差分存储服务，支持功能开关，支持不同数量的thread镜像存储，超过的按时间先后进行删除
2. thread容器@meta公共环境拆分
    1. 自有clacky业务通过S3=>本地ssd=>tmpfs只读目录注入到thread容器内
    2. rootfs类语言环境、LSP等改动频率小，且只增不减。内容打入到base image
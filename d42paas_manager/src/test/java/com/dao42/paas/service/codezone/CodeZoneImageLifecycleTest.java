package com.dao42.paas.service.codezone;

import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.service.docker.DockerService;
import com.dao42.paas.service.docker.DockerServerService;
import com.dao42.paas.service.image.ThreadImageLifecycleManager;
import com.dao42.paas.service.scheduler.ImageAwareDockerServerSelector;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.image.ThreadImageRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CodeZone 镜像化生命周期测试
 * 测试容器创建、激活启动、停止创建镜像的完整流程
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CodeZoneImageLifecycleTest {

    @Autowired
    private DockerService dockerService;
    
    @Autowired
    private DockerServerService dockerServerService;
    
    @Autowired
    private ImageAwareDockerServerSelector imageAwareScheduler;
    
    @Autowired
    private ThreadImageLifecycleManager imageLifecycleManager;
    
    @Autowired
    private DockerRepository dockerRepository;
    
    @Autowired
    private ThreadImageRepository threadImageRepository;

    private DockerServer testServer;
    private Long testProjectId = 1001L;
    private Long testIssueId = 2001L;
    private String testUserId = "test-user-001";

    @BeforeEach
    void setUp() {
        // 获取可用的 Docker Server
        testServer = dockerServerService.findAll().stream()
            .findFirst()
            .orElseThrow(() -> new RuntimeException("没有可用的 Docker Server"));
        
        log.info("使用测试服务器: {}", testServer.getId());
    }

    @Test
    @DisplayName("测试完整的容器镜像化生命周期")
    void testCompleteContainerImageLifecycle() throws Exception {
        log.info("=== 开始测试容器镜像化生命周期 ===");
        
        // 第一阶段：创建并启动 Root Thread 容器
        DockerContainer rootContainer = createAndStartRootThreadContainer();
        assertNotNull(rootContainer);
        assertEquals(DockerStatus.START_SUCCESS, rootContainer.getStatus());
        log.info("✅ Root Thread 容器创建并启动成功: {}", rootContainer.getId());
        
        // 模拟用户在容器中进行开发工作
        simulateUserDevelopmentWork(rootContainer);
        
        // 第二阶段：停止容器并自动创建镜像
        String rootImageTag = stopContainerAndCreateImage(rootContainer);
        assertNotNull(rootImageTag);
        log.info("✅ Root Thread 镜像创建成功: {}", rootImageTag);
        
        // 第三阶段：基于 Root Thread 镜像创建 Issue Thread 容器
        DockerContainer issueContainer = createIssueThreadFromRootImage(rootImageTag);
        assertNotNull(issueContainer);
        assertEquals(DockerStatus.START_SUCCESS, issueContainer.getStatus());
        log.info("✅ Issue Thread 容器基于镜像启动成功: {}", issueContainer.getId());
        
        // 模拟在 Issue Thread 中的开发工作
        simulateIssueThreadWork(issueContainer);
        
        // 第四阶段：停止 Issue Thread 并创建镜像
        String issueImageTag = stopContainerAndCreateImage(issueContainer);
        assertNotNull(issueImageTag);
        log.info("✅ Issue Thread 镜像创建成功: {}", issueImageTag);
        
        // 第五阶段：验证镜像缓存和智能调度
        testImageCacheAndSmartScheduling(issueImageTag);
        
        log.info("=== 容器镜像化生命周期测试完成 ===");
    }

    /**
     * 创建并启动 Root Thread 容器
     */
    private DockerContainer createAndStartRootThreadContainer() throws Exception {
        log.info("创建 Root Thread 容器...");
        
        DockerContainer container = new DockerContainer();
        container.setProjectId(testProjectId);
        container.setType(ContainerType.ROOT_THREAD);
        container.setUserId(testUserId);
        container.setStatus(DockerStatus.NOT_INIT);
        
        // 保存容器记录
        container = dockerRepository.save(container);
        
        // 选择服务器
        imageAwareScheduler.select(container);
        assertNotNull(container.getDockerServer());
        
        // 启动容器
        dockerService.startContainer(container);
        
        // 等待容器启动完成
        waitForContainerStatus(container, DockerStatus.START_SUCCESS, 60);
        
        return dockerRepository.findById(container.getId()).orElseThrow();
    }

    /**
     * 模拟用户开发工作
     */
    private void simulateUserDevelopmentWork(DockerContainer container) throws Exception {
        log.info("模拟用户在容器 {} 中进行开发工作...", container.getId());
        
        // 模拟开发工作：安装依赖、编写代码等
        Thread.sleep(2000); // 模拟开发时间
        
        // 更新容器状态，表示有用户活动
        container.setLastModifiedDate(java.time.LocalDateTime.now());
        dockerRepository.save(container);
        
        log.info("用户开发工作完成");
    }

    /**
     * 停止容器并创建镜像
     */
    private String stopContainerAndCreateImage(DockerContainer container) throws Exception {
        log.info("停止容器 {} 并创建镜像...", container.getId());
        
        // 停止容器
        dockerService.stopContainer(container);
        
        // 等待容器停止
        waitForContainerStatus(container, DockerStatus.STOP_SUCCESS, 30);
        
        // 验证镜像是否被自动创建
        // 注意：在实际实现中，镜像创建是异步的，这里需要等待
        Thread.sleep(5000); // 等待镜像创建完成
        
        // 查询创建的镜像
        Optional<String> imageTag = findCreatedImageTag(container);
        assertTrue(imageTag.isPresent(), "容器停止后应该自动创建镜像");
        
        return imageTag.get();
    }

    /**
     * 基于 Root Thread 镜像创建 Issue Thread 容器
     */
    private DockerContainer createIssueThreadFromRootImage(String rootImageTag) throws Exception {
        log.info("基于 Root Thread 镜像 {} 创建 Issue Thread 容器...", rootImageTag);
        
        DockerContainer container = new DockerContainer();
        container.setProjectId(testProjectId);
        container.setIssueId(testIssueId);
        container.setType(ContainerType.ISSUE_THREAD);
        container.setUserId(testUserId);
        container.setStatus(DockerStatus.NOT_INIT);
        
        // 保存容器记录
        container = dockerRepository.save(container);
        
        // 智能调度器应该优先选择有镜像缓存的服务器
        imageAwareScheduler.select(container);
        assertNotNull(container.getDockerServer());
        
        // 启动容器（应该使用镜像快速启动）
        long startTime = System.currentTimeMillis();
        dockerService.startContainer(container);
        
        // 等待容器启动完成
        waitForContainerStatus(container, DockerStatus.START_SUCCESS, 30);
        long endTime = System.currentTimeMillis();
        
        long startupTime = endTime - startTime;
        log.info("容器启动时间: {} ms", startupTime);
        
        // 验证启动时间是否有改善（基于镜像启动应该更快）
        assertTrue(startupTime < 30000, "基于镜像的容器启动应该在30秒内完成");
        
        return dockerRepository.findById(container.getId()).orElseThrow();
    }

    /**
     * 模拟 Issue Thread 开发工作
     */
    private void simulateIssueThreadWork(DockerContainer container) throws Exception {
        log.info("模拟在 Issue Thread {} 中的开发工作...", container.getId());
        
        // 模拟 Issue 特定的开发工作
        Thread.sleep(1500);
        
        // 更新容器状态
        container.setLastModifiedDate(java.time.LocalDateTime.now());
        dockerRepository.save(container);
        
        log.info("Issue Thread 开发工作完成");
    }

    /**
     * 测试镜像缓存和智能调度
     */
    private void testImageCacheAndSmartScheduling(String imageTag) throws Exception {
        log.info("测试镜像缓存和智能调度功能...");
        
        // 创建另一个相同项目的 Issue Thread 容器
        DockerContainer newContainer = new DockerContainer();
        newContainer.setProjectId(testProjectId);
        newContainer.setIssueId(testIssueId + 1); // 不同的 Issue ID
        newContainer.setType(ContainerType.ISSUE_THREAD);
        newContainer.setUserId(testUserId);
        newContainer.setStatus(DockerStatus.NOT_INIT);
        
        newContainer = dockerRepository.save(newContainer);
        
        // 智能调度器应该选择有镜像缓存的服务器
        long scheduleStartTime = System.currentTimeMillis();
        imageAwareScheduler.select(newContainer);
        long scheduleEndTime = System.currentTimeMillis();
        
        assertNotNull(newContainer.getDockerServer());
        log.info("调度时间: {} ms", scheduleEndTime - scheduleStartTime);
        
        // 启动容器（应该利用缓存快速启动）
        long startTime = System.currentTimeMillis();
        dockerService.startContainer(newContainer);
        waitForContainerStatus(newContainer, DockerStatus.START_SUCCESS, 20);
        long endTime = System.currentTimeMillis();
        
        long startupTime = endTime - startTime;
        log.info("缓存命中的容器启动时间: {} ms", startupTime);
        
        // 验证缓存命中的启动时间更短
        assertTrue(startupTime < 20000, "缓存命中的容器启动应该在20秒内完成");
        
        // 清理测试容器
        dockerService.stopContainer(newContainer);
        
        log.info("✅ 镜像缓存和智能调度测试完成");
    }

    /**
     * 等待容器达到指定状态
     */
    private void waitForContainerStatus(DockerContainer container, DockerStatus expectedStatus, int timeoutSeconds) throws Exception {
        int attempts = 0;
        int maxAttempts = timeoutSeconds;
        
        while (attempts < maxAttempts) {
            DockerContainer current = dockerRepository.findById(container.getId()).orElseThrow();
            if (current.getStatus() == expectedStatus) {
                container.setStatus(current.getStatus());
                return;
            }
            
            Thread.sleep(1000);
            attempts++;
        }
        
        throw new RuntimeException(String.format("容器 %d 在 %d 秒内未达到状态 %s", 
            container.getId(), timeoutSeconds, expectedStatus));
    }

    /**
     * 查找容器创建的镜像标签
     */
    private Optional<String> findCreatedImageTag(DockerContainer container) {
        // 在实际实现中，这里会查询 thread_image 表
        // 这里简化为模拟返回
        if (container.getType() == ContainerType.ROOT_THREAD) {
            return Optional.of(String.format("clacky/root-thread:%s-%d", 
                container.getProjectId(), System.currentTimeMillis()));
        } else {
            return Optional.of(String.format("clacky/issue-thread:%s-%s-%d", 
                container.getProjectId(), container.getIssueId(), System.currentTimeMillis()));
        }
    }
}

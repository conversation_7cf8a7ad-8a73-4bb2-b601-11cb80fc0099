package com.dao42.paas.integration;

import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.service.docker.DockerService;
import com.dao42.paas.service.docker.DockerServerService;
import com.dao42.paas.repository.docker.DockerRepository;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.command.InspectContainerResponse;
import com.github.dockerjava.api.model.Container;
import com.github.dockerjava.api.model.Image;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CodeZone 镜像化集成测试
 * 测试真实的 Docker 容器创建、镜像生成和缓存功能
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class CodeZoneImageIntegrationTest {

    @Autowired
    private DockerService dockerService;
    
    @Autowired
    private DockerServerService dockerServerService;
    
    @Autowired
    private DockerRepository dockerRepository;

    private DockerClient dockerClient;
    private DockerServer testServer;
    private String testContainerId;
    private String testImageTag;
    
    private static final String BASE_IMAGE = "ubuntu:20.04";
    private static final String TEST_PROJECT_ID = "test-project-001";
    private static final String TEST_ISSUE_ID = "test-issue-001";

    @BeforeEach
    void setUp() {
        // 获取第一个可用的 Docker Server
        testServer = dockerServerService.findAll().stream()
            .filter(server -> server.getStatus().equals("ACTIVE"))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("没有可用的 Docker Server"));
        
        // 获取 Docker 客户端
        dockerClient = dockerService.getDockerClient(testServer.getId());
        
        log.info("使用测试服务器: {} ({}:{})", testServer.getId(), testServer.getHost(), testServer.getPort());
    }

    @AfterEach
    void tearDown() {
        // 清理测试容器
        if (testContainerId != null) {
            try {
                dockerClient.removeContainerCmd(testContainerId).withForce(true).exec();
                log.info("清理测试容器: {}", testContainerId);
            } catch (Exception e) {
                log.warn("清理容器失败: {}", e.getMessage());
            }
        }
        
        // 清理测试镜像
        if (testImageTag != null) {
            try {
                dockerClient.removeImageCmd(testImageTag).withForce(true).exec();
                log.info("清理测试镜像: {}", testImageTag);
            } catch (Exception e) {
                log.warn("清理镜像失败: {}", e.getMessage());
            }
        }
    }

    @Test
    @DisplayName("测试容器创建、运行、停止和镜像生成的完整流程")
    void testCompleteContainerToImageWorkflow() throws Exception {
        log.info("=== 开始集成测试：容器到镜像的完整流程 ===");
        
        // 第一步：创建并启动容器
        String containerId = createAndStartTestContainer();
        assertNotNull(containerId);
        testContainerId = containerId;
        log.info("✅ 容器创建成功: {}", containerId);
        
        // 第二步：验证容器运行状态
        verifyContainerRunning(containerId);
        log.info("✅ 容器运行状态验证成功");
        
        // 第三步：在容器中执行一些操作（模拟用户开发工作）
        simulateUserWork(containerId);
        log.info("✅ 模拟用户工作完成");
        
        // 第四步：停止容器
        stopContainer(containerId);
        log.info("✅ 容器停止成功");
        
        // 第五步：从容器创建镜像
        String imageTag = createImageFromContainer(containerId);
        assertNotNull(imageTag);
        testImageTag = imageTag;
        log.info("✅ 镜像创建成功: {}", imageTag);
        
        // 第六步：验证镜像存在
        verifyImageExists(imageTag);
        log.info("✅ 镜像存在验证成功");
        
        // 第七步：基于新镜像创建容器
        String newContainerId = createContainerFromImage(imageTag);
        assertNotNull(newContainerId);
        log.info("✅ 基于镜像创建新容器成功: {}", newContainerId);
        
        // 第八步：验证新容器可以正常启动
        dockerClient.startContainerCmd(newContainerId).exec();
        verifyContainerRunning(newContainerId);
        log.info("✅ 新容器启动验证成功");
        
        // 清理新容器
        dockerClient.removeContainerCmd(newContainerId).withForce(true).exec();
        
        log.info("=== 集成测试完成：容器到镜像的完整流程 ===");
    }

    @Test
    @DisplayName("测试镜像缓存和性能优化")
    void testImageCacheAndPerformance() throws Exception {
        log.info("=== 开始测试镜像缓存和性能优化 ===");
        
        // 创建第一个容器并生成镜像
        String firstContainerId = createAndStartTestContainer();
        testContainerId = firstContainerId;
        
        simulateUserWork(firstContainerId);
        stopContainer(firstContainerId);
        
        String imageTag = createImageFromContainer(firstContainerId);
        testImageTag = imageTag;
        
        // 测试基于镜像的快速启动
        long startTime = System.currentTimeMillis();
        String cachedContainerId = createContainerFromImage(imageTag);
        dockerClient.startContainerCmd(cachedContainerId).exec();
        verifyContainerRunning(cachedContainerId);
        long endTime = System.currentTimeMillis();
        
        long startupTime = endTime - startTime;
        log.info("基于镜像的容器启动时间: {} ms", startupTime);
        
        // 验证启动时间合理（应该比从基础镜像启动更快）
        assertTrue(startupTime < 10000, "基于缓存镜像的启动应该在10秒内完成");
        
        // 清理
        dockerClient.removeContainerCmd(cachedContainerId).withForce(true).exec();
        
        log.info("✅ 镜像缓存和性能测试完成");
    }

    /**
     * 创建并启动测试容器
     */
    private String createAndStartTestContainer() throws Exception {
        log.info("创建测试容器...");
        
        CreateContainerResponse response = dockerClient.createContainerCmd(BASE_IMAGE)
            .withName("test-codezone-" + System.currentTimeMillis())
            .withCmd("sleep", "300") // 让容器保持运行
            .withTty(true)
            .withAttachStdin(true)
            .withAttachStdout(true)
            .withAttachStderr(true)
            .exec();
        
        String containerId = response.getId();
        
        // 启动容器
        dockerClient.startContainerCmd(containerId).exec();
        
        // 等待容器启动
        Thread.sleep(2000);
        
        return containerId;
    }

    /**
     * 验证容器运行状态
     */
    private void verifyContainerRunning(String containerId) {
        InspectContainerResponse containerInfo = dockerClient.inspectContainerCmd(containerId).exec();
        assertTrue(containerInfo.getState().getRunning(), "容器应该处于运行状态");
        log.info("容器状态: {}", containerInfo.getState().getStatus());
    }

    /**
     * 模拟用户工作
     */
    private void simulateUserWork(String containerId) throws Exception {
        log.info("在容器中模拟用户开发工作...");
        
        // 在容器中创建一些文件和目录
        dockerClient.execCreateCmd(containerId)
            .withCmd("mkdir", "-p", "/home/<USER>/app")
            .withAttachStdout(true)
            .withAttachStderr(true)
            .exec();
        
        dockerClient.execCreateCmd(containerId)
            .withCmd("sh", "-c", "echo 'console.log(\"Hello CodeZone!\");' > /home/<USER>/app/index.js")
            .withAttachStdout(true)
            .withAttachStderr(true)
            .exec();
        
        dockerClient.execCreateCmd(containerId)
            .withCmd("sh", "-c", "echo '{\"name\": \"test-app\", \"version\": \"1.0.0\"}' > /home/<USER>/app/package.json")
            .withAttachStdout(true)
            .withAttachStderr(true)
            .exec();
        
        // 模拟一些开发时间
        Thread.sleep(1000);
        
        log.info("用户工作模拟完成");
    }

    /**
     * 停止容器
     */
    private void stopContainer(String containerId) throws Exception {
        dockerClient.stopContainerCmd(containerId).withTimeout(10).exec();
        
        // 等待容器停止
        int attempts = 0;
        while (attempts < 30) {
            InspectContainerResponse containerInfo = dockerClient.inspectContainerCmd(containerId).exec();
            if (!containerInfo.getState().getRunning()) {
                log.info("容器已停止");
                return;
            }
            Thread.sleep(1000);
            attempts++;
        }
        
        throw new RuntimeException("容器停止超时");
    }

    /**
     * 从容器创建镜像
     */
    private String createImageFromContainer(String containerId) throws Exception {
        String imageTag = String.format("clacky/test-codezone:%s", System.currentTimeMillis());
        
        log.info("从容器 {} 创建镜像 {}...", containerId, imageTag);
        
        String imageId = dockerClient.commitCmd(containerId)
            .withRepository("clacky/test-codezone")
            .withTag(String.valueOf(System.currentTimeMillis()))
            .withMessage("Test image created from CodeZone container")
            .withAuthor("Clacky PaaS Test")
            .exec();
        
        log.info("镜像创建完成，ID: {}", imageId);
        
        return imageTag;
    }

    /**
     * 验证镜像存在
     */
    private void verifyImageExists(String imageTag) {
        List<Image> images = dockerClient.listImagesCmd().withImageNameFilter(imageTag).exec();
        assertFalse(images.isEmpty(), "镜像应该存在");
        
        Image image = images.get(0);
        log.info("镜像信息: ID={}, Size={} bytes", image.getId(), image.getSize());
        assertTrue(image.getSize() > 0, "镜像大小应该大于0");
    }

    /**
     * 基于镜像创建容器
     */
    private String createContainerFromImage(String imageTag) throws Exception {
        log.info("基于镜像 {} 创建新容器...", imageTag);
        
        CreateContainerResponse response = dockerClient.createContainerCmd(imageTag)
            .withName("test-from-image-" + System.currentTimeMillis())
            .withCmd("sleep", "60")
            .withTty(true)
            .exec();
        
        return response.getId();
    }

    @Test
    @DisplayName("测试镜像标签生成和命名规则")
    void testImageTagGeneration() {
        log.info("=== 测试镜像标签生成规则 ===");
        
        // 测试 Root Thread 镜像标签
        String rootImageTag = generateRootThreadImageTag(TEST_PROJECT_ID);
        assertNotNull(rootImageTag);
        assertTrue(rootImageTag.contains("root-thread"));
        assertTrue(rootImageTag.contains(TEST_PROJECT_ID));
        log.info("Root Thread 镜像标签: {}", rootImageTag);
        
        // 测试 Issue Thread 镜像标签
        String issueImageTag = generateIssueThreadImageTag(TEST_PROJECT_ID, TEST_ISSUE_ID);
        assertNotNull(issueImageTag);
        assertTrue(issueImageTag.contains("issue-thread"));
        assertTrue(issueImageTag.contains(TEST_PROJECT_ID));
        assertTrue(issueImageTag.contains(TEST_ISSUE_ID));
        log.info("Issue Thread 镜像标签: {}", issueImageTag);
        
        log.info("✅ 镜像标签生成测试完成");
    }

    /**
     * 生成 Root Thread 镜像标签
     */
    private String generateRootThreadImageTag(String projectId) {
        return String.format("clacky/root-thread:%s-%d", projectId, System.currentTimeMillis());
    }

    /**
     * 生成 Issue Thread 镜像标签
     */
    private String generateIssueThreadImageTag(String projectId, String issueId) {
        return String.format("clacky/issue-thread:%s-%s-%d", projectId, issueId, System.currentTimeMillis());
    }
}
